<script lang="ts">
	import {page} from '$app/state';
	import {But<PERSON>} from "@/components/ui/button";
	import {Input} from "@/components/ui/input";
	import {Label} from "@/components/ui/label";
	import {APP} from "@/state/consts";
	import * as Card from "$lib/components/ui/card/index.js";
</script>

<svelte:head>
    <title>Error {page.status}</title>
</svelte:head>

<div class="flex h-screen w-screen items-center justify-center">
    <Card.Root class="mx-auto w-full max-w-sm">
        <Card.Header>
            <img alt="Logo" class="mx-auto h-12 w-auto" src="/logo.png"/>
        </Card.Header>
        <Card.Content class="text-center">
            <h1 class="text-xl font-bold mb-4 text-primary">Error {page.status}</h1>

            <div class="mb-6 text-muted-foreground">
                {#if page.error?.message}
                    <p>{page.error.message}</p>
                {:else}
                    <p>Something went wrong. Please try again later.</p>
                {/if}
            </div>

            <div class="flex gap-4 items-center justify-center">
                <Button onclick={() => history.back()}>Go Back</Button>
                <Button onclick={() => window.location.href = '/'} variant="outline">Go Home</Button>
            </div>
        </Card.Content>
    </Card.Root>
</div>
