<script lang="ts">
	import {page} from "$app/state";
	import {LayoutGrid, LayoutList, Map, Settings2Icon, Users} from "@lucide/svelte";
	import {onMount} from 'svelte';
	import {goto} from '$app/navigation';
	import {But<PERSON>} from "@/components/ui/button";
	import AppSidebar from "$lib/components/sidebar/app.svelte";
	import * as Sidebar from "$lib/components/ui/sidebar/index.js";
	import {Separator} from "$lib/components/ui/separator/index.js";
	import * as Breadcrumb from "$lib/components/ui/breadcrumb/index.js";
	import {setSidebarOpen, sidebarState, initializeSidebar} from "@/state/sidebar.svelte";

	import {auth, IsAuthenticated, checkAuth, logout} from '$lib/state/auth.svelte.js';

	let {children} = $props();
	let isAuthenticated = $derived(IsAuthenticated());
	let pathname = $derived<string>(page.url.pathname);
	let data = $state({
		navMain: [
			{
				title: "Dashboard",
				url: "/dashboard",
				icon: LayoutGrid,
			},
			{
				title: "Locations",
				url: "/locations",
				icon: Map,
			},
			{
				title: "Items",
				url: "/items",
				icon: LayoutList,
			},
			{
				title: "Users",
				url: "/users",
				icon: Users,
			},
			{
				title: "Settings",
				url: "/settings",
				icon: Settings2Icon,
			},
		],
		user: {
			name: "shadcn",
			email: "<EMAIL>",
			avatar: "/avatars/shadcn.jpg",
		},
	})


	onMount(() => {
		if (!auth.isLoading && !isAuthenticated) {
			goto('/login');
		}

		initializeSidebar();
	});

	$effect(() => {
		if (!auth.isLoading && !isAuthenticated) {
			goto('/login');
		}
	});
</script>

{#if auth.isLoading}
    <p>Loading...</p>
{:else if isAuthenticated}
    <Sidebar.Provider bind:open={sidebarState.open} onOpenChange={setSidebarOpen}>
        <AppSidebar bind:data={data}/>
        <Sidebar.Inset>
            <header
                    class="group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear"
            >
                <div class="flex items-center gap-2 px-4">
                    <Sidebar.Trigger class="-ml-1"/>
                    <Separator class="mr-2 data-[orientation=vertical]:h-4" orientation="vertical"/>
                    <Breadcrumb.Root>
                        <Breadcrumb.List>
                            <Breadcrumb.Item class="hidden md:block">
                                <Breadcrumb.Link
                                        href="#">{data.navMain.find(item => item.url === pathname)?.title}</Breadcrumb.Link>
                            </Breadcrumb.Item>
                        </Breadcrumb.List>
                    </Breadcrumb.Root>
                </div>
            </header>

            <div class="p-4">
                {@render children()}
            </div>

        </Sidebar.Inset>
    </Sidebar.Provider>
{:else}
    <p>Redirecting...</p>
{/if}