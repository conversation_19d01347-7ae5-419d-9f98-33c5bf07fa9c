<script lang="ts">
	import Table from "@/components/locations/Table.svelte";
	import Grid from "@/components/locations/Grid.svelte";
	import {Button} from "@/components/ui/button";
	import EditModal from "@/components/locations/modals/edit.svelte";
	import CreateModal from "@/components/locations/modals/new.svelte";
	import {Input} from "@/components/ui/input";
	import {Location} from "@/models/location";
	import {APP} from "@/state/consts";
	import {MapPinPlus, Pencil, Trash} from "@lucide/svelte";
	import {API} from "@/services/api/api";
	import {Grid2x2, Rows3, Search} from "@lucide/svelte";


	let {data} = $props();
	let search = $state("");
	let locations = $state<Location[]>(data.locations);
	let filteredLocations = $derived.by(() => {
		console.log("Search: ", search)
        console.log("Locations: ", locations);

		if (!search || search.trim() === "") return locations;

		return locations.filter((location) => {
			return location.name.toLowerCase().includes(search.toLowerCase()) || location.description.toLowerCase().includes(search.toLowerCase());
		});
	});

	let view: "grid" | "table" = $state("grid");

	let createIsOpen = $state(false);

	let editIsOpen = $state(false);
	let editLocationID = $state<string | null>(null);

	let deleteIsOpen = $state(false);
	let deleteLocationID = $state<string | null>(null);

	function onCreate(location: Location) {
		console.log("Created location: ", location);
		locations.push(location);
		createIsOpen = false;
	}

	function onUpdate(location: Location) {
		console.log("Updated location: ", location);
		locations = locations.map((l) => l.id === location.id ? location : l);
		editIsOpen = false;
	}

	async function setEditLocation(id: string) {
		console.log("Editing location: ", id)
		editLocationID = id;
		editIsOpen = true;
	}

	async function setDeleteLocation(id: string) {
		console.log("Deleting location: ", id)
		deleteLocationID = id;
		deleteIsOpen = true;
	}

</script>

<svelte:head>
    <title>{APP.name} - Locations</title>
</svelte:head>

<CreateModal bind:isOpen={createIsOpen} onCreate={onCreate}/>
<EditModal bind:isOpen={editIsOpen} bind:locationID={editLocationID} onUpdate={onUpdate}/>

<div class="flex flex-col gap-4">
    <div class="flex justify-between">
        <Button onclick={() => createIsOpen = true}>
            <MapPinPlus/>
            New Location
        </Button>

        <div>
            <Button variant={view === 'grid' ? 'default' : 'ghost'} title="Grid View" onclick={() => view = 'grid'}>
                <Grid2x2 />
            </Button>

            <Button variant={view === 'table' ? 'default' : 'ghost'} title="Table View" onclick={() => view = 'table'}>
                <Rows3 />
            </Button>
        </div>
    </div>


    <div class="flex gap-2 items-center justify-between" >
        <Input placeholder="Search ..." class="w-full" bind:value={search}>
            <Search />
        </Input>
    </div>

    {#if view === 'grid'}
        <Grid bind:locations={filteredLocations} onEdit={setEditLocation} onDelete={setDeleteLocation} />
    {:else if view === 'table'}
        <Table bind:locations={filteredLocations} onEdit={setEditLocation} onDelete={setDeleteLocation} />
    {/if}
</div>