<script lang="ts">
	import '../app.css';
	import {goto} from "$app/navigation";
	import {page} from "$app/state";

	import {Button} from "@/components/ui/button";
	import AppSidebar from "$lib/components/sidebar/app.svelte";
	import * as Sidebar from "$lib/components/ui/sidebar/index.js";
	import {Separator} from "$lib/components/ui/separator/index.js";
	import * as Breadcrumb from "$lib/components/ui/breadcrumb/index.js";
	import {setSidebarOpen, sidebarState} from "@/state/sidebar.svelte";

	import {onMount} from 'svelte';
	import {auth, IsAuthenticated, checkAuth, logout} from '$lib/state/auth.svelte.js';

	let {children} = $props();

	onMount(async () => {
		if (page.error) {
			return
		}

		const isLoggedIn = await checkAuth();
		if (isLoggedIn) {
			await goto('/dashboard');
		}

		await goto('/login');
	});
</script>

{#if auth.isLoading}
    <p>Loading...</p>
{:else}
    {@render children()}
{/if}
