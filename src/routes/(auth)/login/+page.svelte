<script lang="ts">
	import {goto} from "$app/navigation";
	import {User} from "@/models/user";
	import {API} from "@/services/api/api";
	import {APP} from "@/state/consts";

	import {Button} from "$lib/components/ui/button/index.js";
	import * as Card from "$lib/components/ui/card/index.js";
	import {Input} from "$lib/components/ui/input/index.js";
	import {Label} from "$lib/components/ui/label/index.js";
	import {sleep} from '$lib/utils';

	import {login} from '$lib/state/auth.svelte.js';

	let username = $state("");
	let password = $state("");
	let isValid = $state(false);
	let error = $state<string | null>(null);

	$effect(() => {
		isValid = username.length >= 3 && password.length >= 3;
	});

	async function Login() {
		console.log("Logging in...");

		const {data, err} = await API.Auth.Login(username, password);
		if (err || !data) {
			error = err?.message || "Something went wrong. Please try again later.";
			return;
		}

		login(data.user, data.token);
		await goto('/dashboard');

		await sleep(1000);
	}
</script>

<svelte:head>
    <title>{APP.name} - Login</title>
</svelte:head>

<div class="flex h-screen w-screen items-center justify-center">
    <Card.Root class="mx-auto w-full max-w-sm">
        <Card.Header>
            <img alt="Logo" class="mx-auto h-12 w-auto" src="/logo.png"/>
        </Card.Header>
        <Card.Content>
            <div class="grid gap-4">
                <div class="grid gap-2">
                    <Label for="username">Username</Label>
                    <Input bind:value={username} id="username" placeholder="username" required type="username"/>
                </div>
                <div class="grid gap-2">
                    <Label for="password">Password</Label>
                    <Input bind:value={password} id="password" required type="password"/>
                </div>

                <Button class="w-full" disabled={!isValid} onClickPromise={Login}>Login</Button>
            </div>

            {#if error}
                <p class="text-destructive text-sm font-medium mt-4">{error}</p>
            {/if}

            <div class="mt-4 text-center text-sm">
                {APP.name} - { new Date().getFullYear() }
            </div>
        </Card.Content>
    </Card.Root>
</div>
