import {Type} from 'class-transformer';

export class Location {
	id: string = "";
	name: string = "";
	description: string = "";
	images: string[] = [];

	@Type(() => Item)
	items: Item[] = [];
}

export class Item {
	id: string = "";
	name: string = "";
	description: string = "";
	images: string[] = [];
	quantity: number = 0;

	@Type(() => Location)
	location: Location = new Location();
}

export class Movement {
	id: string = "";
	reason: MovementReason = MovementReason.Unknown;
	description: string = "";
}

export enum MovementReason {
	Unknown = "unknown",
	Created = "created",
	Deleted = "deleted",
	Moved = "moved",
	Adjustment = "adjustment",
}