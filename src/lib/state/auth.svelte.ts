import {browser} from '$app/environment';
import {goto} from "$app/navigation";
import type {User} from "@/models/user";
import {API} from "@/services/api/api";

const tokenKey = "x-token" as const;

export const auth: { user: User | null, token: string | null, isLoading: boolean } = $state({
	user: null,
	token: null,
	isLoading: false
});

let isAuthenticated: boolean = $derived(!!auth.user && !!auth.token);

export async function InitializeAuth() {
	if (!browser) {
		return
	}

	auth.token = localStorage.getItem(tokenKey);
	await checkAuth();
}

export function IsAuthenticated() {
	return isAuthenticated;
}

export function login(u: User, t: string) {
	auth.user = u;
	auth.token = t;

	if (browser) {
		localStorage.setItem(tokenKey, t);
	}
}

export function logout() {
	auth.user = null;
	auth.token = null;

	if (browser) {
		localStorage.removeItem(tokenKey);
	}
}

export async function checkAuth() {
	if (!auth.token) return false;

	auth.isLoading = true;

	const {data, err} = await API.Auth.Verify();
	if (err || !data) {
		logout();
		auth.isLoading = false;
		return false;
	}

	auth.user = data;
	auth.isLoading = false;

	return true;
}

export async function onUnauthorized<T>(response: Response): Promise<T | undefined> {
	logout();
	await goto('/login');
	return null as T;
}