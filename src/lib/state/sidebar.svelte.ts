import { browser } from '$app/environment';

const SIDEBAR_STORAGE_KEY = 'sidebar:state';

export const sidebarState = $state({
    open: true
});

export function initializeSidebar() {
    if (!browser) return;
    
    const savedState = localStorage.getItem(SIDEBAR_STORAGE_KEY);
    if (savedState !== null) {
        sidebarState.open = savedState === 'true';
    }
}

export function setSidebarOpen(open: boolean) {
    sidebarState.open = open;
    
    if (browser) {
        localStorage.setItem(SIDEBAR_STORAGE_KEY, open.toString());
    }
}

export function toggleSidebar() {
    setSidebarOpen(!sidebarState.open);
}