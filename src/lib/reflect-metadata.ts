import {type ClassTransformOptions, plainToInstance} from 'class-transformer';

export const DEFAULT_CLASS_TRANSFORM_OPTIONS: ClassTransformOptions = {
	excludeExtraneousValues: false,
	enableImplicitConversion: true,
	exposeDefaultValues: true,
	exposeUnsetFields: true,
};

export function transformToClass<T>(
	ClassType: new (...args: any[]) => T,
	data: any,
	options: ClassTransformOptions = {}
): T {
	return plainToInstance(ClassType, data, {
		...DEFAULT_CLASS_TRANSFORM_OPTIONS,
		...options
	});
}