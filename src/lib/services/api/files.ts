import {API_BASE} from "@/services/api/api";
import {APIError, HTTP} from "@/services/api/http";
import {type Result, tryCatch} from "@/try-catch";

export class Files {
	constructor(private readonly http: HTTP) {}

	async Upload(file: File): Promise<Result<{ url: string }, APIError>> {
		const fileBytes = await file.arrayBuffer();
		console.log("byes:", fileBytes);

		return tryCatch(
			this.http.request('/files', {
				method: 'POST',
				body: fileBytes,
				headers: {
					'Content-Type': 'application/octet-stream'
				},
				requiresAuth: true,
			})
		);
	}

	async GetByID(id: string): Promise<Result<Blob, APIError>> {
		return tryCatch(
			this.http.request(`/files/${id}`, {
				responseType: 'blob',
				key: `file-${id}`,
				requiresAuth: true
			})
		);
	}

	async GetImageURL(id: string): Promise<string> {
		const {data, err} = await this.GetByID(id)
		if (err || !data) {
			console.error("Failed to fetch image", err);
			return "";
		}

		return URL.createObjectURL(data as Blob);
	}
}
