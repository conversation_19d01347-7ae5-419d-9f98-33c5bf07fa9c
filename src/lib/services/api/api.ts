import {browser} from "$app/environment";
import {HTTP} from "@/services/api/http";
import {Auth} from "@/services/api/auth";
import {Files} from "@/services/api/files";
import {Locations} from "@/services/api/locations";
import {onUnauthorized} from "@/state/auth.svelte";

export const API_BASE = "http://localhost:8080/v1";

const http = new HTTP(API_BASE, onUnauthorized);

export const API = {
	Auth: new Auth(http),
	Files: new Files(http),
	Locations: new Locations(http)
};