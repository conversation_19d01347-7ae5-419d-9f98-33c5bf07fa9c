import type {User} from "@/models/user";
import {APIError, HTTP} from "@/services/api/http";
import {tryCatch, type Result} from "@/try-catch";

export class Auth {
	constructor(private readonly http: HTTP) {}

	async Login(username: string, password: string): Promise<Result<{ token: string; user: User }, APIError>> {
		return tryCatch(
			this.http.request('/auth/login', {
				method: 'POST',
				body: JSON.stringify({email: username, password}),
			})
		);
	}

	async Verify(): Promise<Result<User, APIError>> {
		return tryCatch(
			this.http.request('/auth/me', {
				requiresAuth: true,
				key: 'auth:verify',
				ttl: 30000,
			})
		);
	}
}