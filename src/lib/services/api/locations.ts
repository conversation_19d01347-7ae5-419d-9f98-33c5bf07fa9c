import {Location} from "@/models/location";
import {transformToClass} from "@/reflect-metadata";
import {APIError, HTTP} from "@/services/api/http";
import {tryCatch, type Result} from "@/try-catch";

export class Locations {
	constructor(private readonly http: HTTP) {}

	async List(): Promise<Result<Location[], APIError>> {
		return tryCatch(
			this.http.request('/locations', {
				method: 'GET',
				requiresAuth: true,
				key: 'locations:list',
				ttl: 2000,
				transform: (data: any) => transformToClass(Location, data),
			})
		);
	}

	async GetByID(id: string): Promise<Result<Location, APIError>> {
		return tryCatch(
			this.http.request(`/locations/${id}`, {
				requiresAuth: true,
				key: `locations:${id}`,
				ttl: 3000,
			})
		);
	}

	async Create(location: Location): Promise<Result<Location, APIError>> {
		return tryCatch(
			this.http.request('/locations', {
				method: 'POST',
				requiresAuth: true,
				body: JSON.stringify(location),
				transform: (data: any) => transformToClass(Location, data),
			})
		);
	}

	async Update(location: Location): Promise<Result<Location, APIError>> {
		return tryCatch(
			this.http.request(`/locations/${location.id}`, {
				method: 'PUT',
				requiresAuth: true,
				body: JSON.stringify(location),
				transform: (data: any) => transformToClass(Location, data),
			})
		);
	}
}