import {auth} from "@/state/auth.svelte";
import superFetch from "@/superfetch";
import {tryCatch} from "@/try-catch";

interface RequestOptions extends RequestInit {
	headers?: Record<string, string>;
	requiresAuth?: boolean;
	responseType?: 'json' | 'text' | 'blob' | 'arrayBuffer' | 'formData';
	transform?: (data: any) => any;
	key?: string;
	ttl?: number;
}

export class APIError extends Error {
	constructor(
		message: string,
		public status: number,
		public response?: Response
	) {
		super(message);
		this.name = 'APIError';
	}
}

export class HTTP {
	private readonly baseURL: string;
	private readonly onUnauthorized: (<T>(response: Response) => Promise<T | undefined>) | undefined;

	constructor(baseURL: string, onUnauthorized?: (<T>(response: Response) => Promise<T | undefined>) | undefined) {
		this.baseURL = baseURL;
		this.onUnauthorized = onUnauthorized;
	}

	async request<T>(endpoint: string, options: RequestOptions): Promise<T> {
		const {
			requiresAuth = false,
			responseType = 'json',
			key = undefined,
			ttl = 300000,
			...fetchOptions
		} = options;

		const headers: Record<string, string> = {
			'Content-Type': 'application/json',
			...fetchOptions?.headers,
		};

		if (requiresAuth) {
			headers.Authorization = `Bearer ${auth.token}`;
		}

		const response = await superFetch.query({
			url: this.baseURL + endpoint,
			...fetchOptions,
			headers,
			key,
			ttl,
		});

		if (!response) {
			throw new APIError('Failed to fetch', 500);
		}

		return this.handleResponse<T>(response, options.responseType, options.transform);
	}

	async handleResponse<T>(response: Response, responseType: RequestOptions['responseType'] = 'json', dataTransformer?: (data: T) => T): Promise<T> {
		if (!response.ok) {
			if (response.status === 401 && this.onUnauthorized) {
				const result = await this.onUnauthorized<T>(response);
				if (result !== undefined) {
					return result;
				}
			}

			let msg: string = `request failed: ${response.status} ${response.statusText}`;

			const {data} = await tryCatch(response.json());
			if (data) {
				msg = data.message || data.error || msg;
			}

			throw new APIError(msg, response.status, response);
		}

		if (response.status === 204 || response.headers.get('content-length') === '0') {
			return null as T;
		}

		const {data, err} = await tryCatch(response[responseType]());
		if (err) {
			throw new APIError(`Failed to parse response: ${err.message || "Unknown error"}`, response.status, response);
		}

		if (dataTransformer && data !== undefined) {
			return dataTransformer(data);
		}

		return data;
	}
}
