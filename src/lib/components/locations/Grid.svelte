<script lang="ts">
	import {Pencil, Trash} from "@lucide/svelte";
	import {Button} from "@/components/ui/button";
	import * as Card from "$lib/components/ui/card/index.js";
	import * as Tooltip from "$lib/components/ui/tooltip/index.js";

	import {API} from "@/services/api/api";
	import {Location} from "@/models/location";

	type Props = {
		locations: Location[];
		onEdit:  (id: string) => void;
		onDelete: (id: string) => void;
	}

	let {locations = $bindable([]), onEdit = () => {}, onDelete = () => {} } = $props();

</script>

<div class="flex flex-grow flex-wrap gap-4">
    {#each locations as location (location.id)}
        <Card.Root class="w-full max-w-sm">
            <Card.Header>
                <Card.Title class="flex justify-between items-start overflow-hidden">
                    <div class="flex-1">
                        <Tooltip.Provider>
                            <Tooltip.Root>
                                <Tooltip.Trigger>
                                    <p class="text-lg">{location.name}</p>
                                </Tooltip.Trigger>
                                <Tooltip.Content>
                                    <p>{location.name}</p>
                                </Tooltip.Content>
                            </Tooltip.Root>
                        </Tooltip.Provider>

                    </div>
                    <div class="flex gap-0.5" >
                        <Button variant="outline" size="icon" class="h-8 w-8" onclick={() => onEdit(location.id)}>
                            <Pencil class="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="icon" class="h-8 w-8" onclick={() => onDelete(location.id)}>
                            <Trash class="h-4 w-4" color="red" />
                        </Button>
                    </div>
                </Card.Title>

                <div class="flex items-center justify-center h-32">
                    {#if location.images?.length > 0}
                        {#await API.Files.GetImageURL(location.images[0]) then imageUrl}
                            {#if imageUrl}
                                <img src={imageUrl} alt={location.name} class="w-32 h-32 rounded-md object-cover"/>
                            {:else}
                                <div class="w-32 h-32 bg-muted rounded-md flex items-center justify-center">
                                    <span class="text-muted-foreground text-sm">No image</span>
                                </div>
                            {/if}
                        {/await}
                    {:else}
                        <div class="w-32 h-32 bg-muted rounded-md flex items-center justify-center">
                            <span class="text-muted-foreground text-sm">No image</span>
                        </div>
                    {/if}
                </div>
            </Card.Header>
            <Card.Content class="flex flex-col justify-between min-h-[4rem]">
                <div>
                    <p class="text-sm text-muted-foreground mb-1">Description</p>
                    <p class="text-sm line-clamp-3">{location.description || 'No description available'}</p>
                </div>
            </Card.Content>
        </Card.Root>
    {/each}
</div>