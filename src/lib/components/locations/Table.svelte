<script lang="ts">
    import * as Table from "$lib/components/ui/table/index.js";
    import {Button} from "@/components/ui/button";
	import {Location} from "@/models/location";
	import {API} from "@/services/api/api";

	import {Pencil, Trash} from "@lucide/svelte";

	type Props = {
		locations: Location[];
		onEdit:  (id: string) => void;
		onDelete: (id: string) => void;
    }

	let {locations = $bindable([]), onEdit = () => {}, onDelete = () => {} } = $props();

</script>

<Table.Root>
        <Table.Header>
            <Table.Row>
                <Table.Head class="w-40">Picture</Table.Head>
                <Table.Head>Name</Table.Head>
                <Table.Head>Description</Table.Head>
                <Table.Head>Items #</Table.Head>
                <Table.Head class="text-right">Actions</Table.Head>
            </Table.Row>
        </Table.Header>
        <Table.Body>
            {#each locations as location (location.id)}
                <Table.Row>
                    <Table.Cell>
                        {#if location.images?.length > 0}
                            {#await API.Files.GetImageURL(location.images[0]) then imageUrl}
                                {#if imageUrl}
                                    <img src={imageUrl} alt={location.name} class="w-32 rounded-md object-cover"/>
                                {/if}
                            {/await}
                        {/if}
                    </Table.Cell>

                    <Table.Cell>{location.name}</Table.Cell>
                    <Table.Cell>{location.description}</Table.Cell>
                    <Table.Cell>{location.items?.length}</Table.Cell>

                    <Table.Cell class="text-right">
                        <Button class="cursor-pointer" variant="outline" size="icon"
                                onClickPromise={() => onEdit(location.id)}>
                            <Pencil/>
                        </Button>

                        <Button class="cursor-pointer" variant="outline" size="icon"
                                onClickPromise={() => onDelete(location.id)}>
                            <Trash color="red"/>
                        </Button>

                    </Table.Cell>

                </Table.Row>
            {/each}
        </Table.Body>
    </Table.Root>