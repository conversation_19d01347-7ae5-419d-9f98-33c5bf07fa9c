<script lang="ts">
	import { Button } from "@/components/ui/button";
	import * as Modal from "$lib/components/ui/modal";
	import { Input } from "@/components/ui/input";
	import { Label } from "@/components/ui/label";
	import {ScrollArea} from "@/components/ui/scroll-area";
	import {API} from "@/services/api/api";
	import {Location} from "@/models/location";
	import {File} from "@/models/file";
	import FileDrop from "@/components/files/fileDrop.svelte";

	import { Skeleton } from "$lib/components/ui/skeleton/index.js";

	type Props = {
		isOpen: boolean;
		locationID: string | null;
		onUpdate: (location: Location) => void;
	};

	let { isOpen = $bindable(false), locationID = $bindable<string | null>(null), onUpdate = (location: Location) => {} } : Props = $props();

	let isLoading = $state(false);
	let oldName = $state<string | null>(null);
	let error = $state<string | null>(null);
	let location = $state<Location | null>(null);
	let images = $state<string[]>([]);

	function setError(err: string | null) {
	    if (err) {
			console.error(err);
		    error = err;
			location = null;
			isLoading = false;
			oldName = null;
			return;
		}

		error = null;
		isLoading = false;
	}

	async function onSubmit() {
		if (!location) return;

		location.images = images;
		const {data, err} = await API.Locations.Update(location);
		if (err || !data) {
			setError(err?.message || "Failed to update location");
			return;
		}

		onUpdate(data);
		isOpen = false;
	}

	$effect(async () => {
		isLoading = true;

		if (!locationID) {
			location = null;
			setError(null);
			return;
		}

		const {data, err} = await API.Locations.GetByID(locationID);
		if (err || !data) {
			setError(error = err?.message || "Failed to load location");
			return
		}

        location = data;
		oldName = data.name;
		images = data.images || [];

		isLoading = false;
	});

</script>


<Modal.Root bind:open={isOpen}>
    <Modal.Content>
        <Modal.Header>
            <Modal.Title>Edit location - {oldName}</Modal.Title>
            <Modal.Description/>
        </Modal.Header>

            {#if error}
                <p class="text-destructive text-sm font-medium mt-4">{error}</p>
            {/if}

            {#if isLoading}
                <Skeleton class="h-6 w-full" />
                <Skeleton class="h-6 w-full" />
                <Skeleton class="h-6 w-full" />
                <Skeleton class="h-6 w-full" />
            {:else}
                {#if location}
                    <div class="flex flex-col gap-4 py-4 p-2">
                        <div class="flex flex-col gap-2">
                            <Label for="name" class="text-right">
                                Name
                                <span class="text-destructive">*</span>
                            </Label>
                            <Input id="name" bind:value={location.name} class="col-span-3" required />
                        </div>

                        <div class="flex flex-col gap-2">
                            <Label for="description" class="text-right">
                                Description
                            </Label>
                            <Input id="description" bind:value={location.description} class="col-span-3" />
                        </div>

                            <div class="flex flex-col gap-2">
                                <Label for="images" class="text-right">Images ({images.length})</Label>

                                <ScrollArea class="h-72">
                                    <FileDrop bind:existing={images} />
                                </ScrollArea>
                            </div>
                    </div>
                {/if}
            {/if}

        <Modal.Footer>
            <Button variant="outline" onclick={() => isOpen = false} loading={isLoading}>Cancel</Button>
            <Button type="submit" onclick={onSubmit} loading={isLoading}>Save changes</Button>
        </Modal.Footer>
    </Modal.Content>
</Modal.Root>