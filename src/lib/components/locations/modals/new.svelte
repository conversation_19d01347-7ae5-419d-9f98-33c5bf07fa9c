<script lang="ts">
	import { Button } from "@/components/ui/button";
	import * as Modal from "$lib/components/ui/modal";
	import { Input } from "@/components/ui/input";
	import { Label } from "@/components/ui/label";
	import {ScrollArea} from "@/components/ui/scroll-area";
	import {API} from "@/services/api/api";
	import {Location} from "@/models/location";
	import {File} from "@/models/file";
	import FileDrop from "@/components/files/fileDrop.svelte";

	import { Skeleton } from "$lib/components/ui/skeleton/index.js";

	type Props = {
		isOpen: boolean;
		onCreate: (location: Location) => void;
	};

	let { isOpen = $bindable(false), onCreate = $bindable(() => {}) } : Props = $props();

	let isLoading = $state(false);
	let error = $state<string | null>(null);
	let location = $state<Location | null>(null);
	let images = $state<string[]>([]);

	$effect(() => {
		isLoading = true;

		if (isOpen) {
			location = new Location();
			error = null;
			images = [];
		}

		isLoading = false;
	});

	function setError(err: string | null) {
		if (err) {
			console.error(err);
			error = err;
			location = null;
			isLoading = false;
			return;
		}

		error = null;
		isLoading = false;
	}

	async function onSubmit() {
		if (!location) return;

		location.images = images;
		const {data, err} = await API.Locations.Create(location);
		if (err || !data) {
			setError(err?.message || "Failed to create location");
			return;
		}

		onCreate(data);
		isOpen = false;
	}


</script>

<Modal.Root bind:open={isOpen}>
    <Modal.Content>
        <Modal.Header>
            <Modal.Title>Create location</Modal.Title>
            <Modal.Description/>
        </Modal.Header>

        {#if error}
            <p class="text-destructive text-sm font-medium mt-4">{error}</p>
        {/if}

        {#if isLoading}
            <Skeleton class="h-6 w-full" />
            <Skeleton class="h-6 w-full" />
            <Skeleton class="h-6 w-full" />
            <Skeleton class="h-6 w-full" />
        {:else}
            {#if location}
                <div class="flex flex-col gap-4 py-4 p-2">
                    <div class="flex flex-col gap-2">
                        <Label for="name" class="text-right">
                            Name
                            <span class="text-destructive">*</span>
                        </Label>
                        <Input id="name" bind:value={location.name} class="col-span-3" required />
                    </div>

                    <div class="flex flex-col gap-2">
                        <Label for="description" class="text-right">
                            Description
                        </Label>
                        <Input id="description" bind:value={location.description} class="col-span-3" />
                    </div>

                    <div class="flex flex-col gap-2">
                        <Label for="images" class="text-right">Images ({images.length})</Label>

                        <ScrollArea class="h-72">
                            <FileDrop bind:existing={images} />
                        </ScrollArea>
                    </div>
                </div>
            {/if}
        {/if}

        <Modal.Footer>
            <Button variant="outline" onclick={() => isOpen = false} loading={isLoading}>Cancel</Button>
            <Button type="submit" onclick={onSubmit} loading={isLoading} disabled={error != null}>Save changes</Button>
        </Modal.Footer>
    </Modal.Content>
</Modal.Root>