<script lang="ts">
	import * as Collapsible from "$lib/components/ui/collapsible/index.js";
	import * as Sidebar from "$lib/components/ui/sidebar/index.js";
	import ChevronRightIcon from "@lucide/svelte/icons/chevron-right";
	import { page } from '$app/state';
	import type {Component} from "svelte";

	let { items }: {items: {title: string, url: string, icon: Component, isActive?: boolean }[]} = $props();
	let pathname = $derived<string>(page.url.pathname);

	let rItems = $derived(items.map(item => ({
		...item, 
		isActive: pathname === item.url
	})));
</script>

<Sidebar.Group>
    <!--    <Sidebar.GroupLabel>Platform</Sidebar.GroupLabel>-->
    <Sidebar.Menu>
        {#each rItems as item (item.title)}
            <Collapsible.Root open={item.isActive} class="group/collapsible">
                {#snippet child({props})}
                    <Sidebar.MenuItem {...props}>
                        <Sidebar.MenuButton tooltipContent={item.title}>
                            {#snippet child({ props })}
                                <div class="{item.isActive ? 'underline text-primary font-semibold' : ''}">
                                    <a href={item.url} {...props} >
                                        <item.icon />
                                        <span>{item.title}</span>
                                    </a>
                                </div>
                            {/snippet}
                        </Sidebar.MenuButton>
                    </Sidebar.MenuItem>
                {/snippet}
            </Collapsible.Root>
        {/each}
    </Sidebar.Menu>
</Sidebar.Group>