<script lang="ts">
	import NavHeader from "./nav-header.svelte";
	import NavMain from "./nav-main.svelte";
	import NavUser from "./nav-user.svelte";
	import * as Sidebar from "$lib/components/ui/sidebar/index.js";
	import type {ComponentProps} from "svelte";
	import {Map, Settings2Icon, LayoutList, CommandIcon, Users, LayoutGrid} from "@lucide/svelte";
	let {
		ref = $bindable(null),
		collapsible = "icon",
        data = $bindable({ navMain: [], user: {} }),
		...restProps
	}: ComponentProps<typeof Sidebar.Root> = $props();
</script>

<Sidebar.Root {...restProps} {collapsible}>
    <Sidebar.Header>
        <NavHeader/>
    </Sidebar.Header>
    <Sidebar.Content>
        <NavMain items={data.navMain}/>
    </Sidebar.Content>
    <Sidebar.Footer>
        <NavUser user={data.user}/>
    </Sidebar.Footer>
    <Sidebar.Rail/>
</Sidebar.Root>