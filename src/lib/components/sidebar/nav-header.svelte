<script lang="ts">
	import {useSidebar} from "$lib/components/ui/sidebar/index.js";

	const sidebar = useSidebar();
	let isCollapsedDelayed = $state<boolean>(false);

	$effect(() => {
		const isCollapsed = sidebar.state === "collapsed";

		setTimeout(() => {
			isCollapsedDelayed = isCollapsed;
		}, 75);
	});
</script>

<div class="flex items-center justify-center" title="Home">
    <a class="w-40 {isCollapsedDelayed ? 'hidden' : 'block'}" href="/">
        <img alt="Logo" src={"/logo.png"} fetchpriority="high"/>
    </a>

    <a class="w-6 {!isCollapsedDelayed ? 'hidden' : 'block'}" href="/">
        <img alt="Logo" src={"/favicon.png"}/>
    </a>
</div>